2025-07-08 20:36:05,954 - werkzeug - WARNING -  * Debugger is active!
2025-07-08 20:36:05,957 - werkzeug - INFO -  * Debugger PIN: 110-************-07-08 20:36:23,059 - werkzeug - INFO -  * Detected change in 'D:\\project\\TradingAgents\\api_server.py', reloading
2025-07-08 20:36:25,685 - werkzeug - WARNING -  * Debugger is active!
2025-07-08 20:36:25,688 - werkzeug - INFO -  * Debugger PIN: 110-************-07-08 20:36:42,815 - werkzeug - INFO -  * Detected change in 'D:\\project\\TradingAgents\\api_server.py', reloading
2025-07-08 20:36:45,503 - werkzeug - WARNING -  * Debugger is active!
2025-07-08 20:36:45,506 - werkzeug - INFO -  * Debugger PIN: 110-************-07-08 20:36:59,592 - werkzeug - INFO -  * Detected change in 'D:\\project\\TradingAgents\\api_server.py', reloading
2025-07-08 20:37:02,260 - werkzeug - WARNING -  * Debugger is active!
2025-07-08 20:37:02,263 - werkzeug - INFO -  * Debugger PIN: 110-************-07-08 20:37:47,587 - werkzeug - INFO -  * Detected change in 'D:\\project\\TradingAgents\\api_server.py', reloading
2025-07-08 20:37:50,186 - werkzeug - WARNING -  * Debugger is active!
2025-07-08 20:37:50,189 - werkzeug - INFO -  * Debugger PIN: 110-************-07-08 20:38:25,720 - werkzeug - INFO -  * Detected change in 'D:\\project\\TradingAgents\\api_server.py', reloading
2025-07-08 20:38:28,493 - werkzeug - WARNING -  * Debugger is active!
2025-07-08 20:38:28,497 - werkzeug - INFO -  * Debugger PIN: 110-************-07-08 20:38:57,972 - werkzeug - INFO -  * Detected change in 'D:\\project\\TradingAgents\\api_server.py', reloading
2025-07-08 20:39:01,534 - werkzeug - WARNING -  * Debugger is active!
2025-07-08 20:39:01,538 - werkzeug - INFO -  * Debugger PIN: 110-************-07-08 20:40:23,046 - werkzeug - INFO -  * Detected change in 'D:\\project\\TradingAgents\\api_server.py', reloading
2025-07-08 20:40:26,082 - werkzeug - WARNING -  * Debugger is active!
2025-07-08 20:40:26,085 - werkzeug - INFO -  * Debugger PIN: 110-************-07-08 20:41:18,112 - werkzeug - INFO -  * Detected change in 'D:\\project\\TradingAgents\\api_server.py', reloading
2025-07-08 20:41:21,077 - werkzeug - WARNING -  * Debugger is active!
2025-07-08 20:41:21,080 - werkzeug - INFO -  * Debugger PIN: 110-************-07-08 20:41:47,610 - werkzeug - INFO -  * Detected change in 'D:\\project\\TradingAgents\\api_server.py', reloading
2025-07-08 20:41:50,516 - werkzeug - WARNING -  * Debugger is active!
2025-07-08 20:41:50,519 - werkzeug - INFO -  * Debugger PIN: 110-************-07-08 20:42:14,857 - werkzeug - INFO -  * Detected change in 'D:\\project\\TradingAgents\\api_server.py', reloading
2025-07-08 20:42:17,799 - __main__ - INFO - TradingAgents API 服务器启动
2025-07-08 20:42:17,804 - werkzeug - WARNING -  * Debugger is active!
2025-07-08 20:42:17,808 - werkzeug - INFO -  * Debugger PIN: 110-************-07-08 21:23:59,123 - __main__ - INFO - 收到请求: POST http://localhost:5000/api/analysis/analyze
2025-07-08 21:23:59,130 - werkzeug - INFO - 127.0.0.1 - - [08/Jul/2025 21:23:59] "[35m[1mPOST /api/analysis/analyze HTTP/1.1[0m" 500 -
2025-07-08 21:36:09,249 - werkzeug - INFO -  * Detected change in 'D:\\project\\TradingAgents\\api_server.py', reloading
2025-07-08 21:36:12,092 - __main__ - INFO - TradingAgents API 服务器启动
2025-07-08 21:36:12,096 - werkzeug - WARNING -  * Debugger is active!
2025-07-08 21:36:12,099 - werkzeug - INFO -  * Debugger PIN: 110-************-07-08 21:36:46,310 - werkzeug - INFO -  * Detected change in 'D:\\project\\TradingAgents\\api_server.py', reloading
2025-07-08 21:36:48,990 - __main__ - INFO - TradingAgents API 服务器启动
2025-07-08 21:36:48,994 - werkzeug - WARNING -  * Debugger is active!
2025-07-08 21:36:48,997 - werkzeug - INFO -  * Debugger PIN: 110-************-07-08 21:36:58,051 - werkzeug - INFO -  * Detected change in 'D:\\project\\TradingAgents\\api_server.py', reloading
2025-07-08 21:37:00,849 - __main__ - INFO - TradingAgents API 服务器启动
2025-07-08 21:37:00,854 - werkzeug - WARNING -  * Debugger is active!
2025-07-08 21:37:00,857 - werkzeug - INFO -  * Debugger PIN: 110-************-07-08 21:37:17,962 - werkzeug - INFO -  * Detected change in 'D:\\project\\TradingAgents\\api_server.py', reloading
2025-07-08 21:37:20,713 - __main__ - INFO - TradingAgents API 服务器启动
2025-07-08 21:37:20,717 - werkzeug - WARNING -  * Debugger is active!
2025-07-08 21:37:20,720 - werkzeug - INFO -  * Debugger PIN: 110-************-07-08 21:39:03,502 - __main__ - INFO - 收到请求: POST http://localhost:5000/api/analysis/analyze
2025-07-08 21:39:03,504 - __main__ - ERROR - 操作失败: 执行股票分析
2025-07-08 21:39:03,504 - __main__ - ERROR - 错误信息: 415 Unsupported Media Type: Did not attempt to load JSON data because the request Content-Type was not 'application/json'.
2025-07-08 21:39:03,504 - __main__ - ERROR - 错误详情: Traceback (most recent call last):
  File "D:\project\TradingAgents\api_server.py", line 524, in post
    data = request.get_json()
           ^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\werkzeug\wrappers\request.py", line 604, in get_json
    return self.on_json_loading_failed(None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\flask\wrappers.py", line 214, in on_json_loading_failed
    return super().on_json_loading_failed(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\werkzeug\wrappers\request.py", line 647, in on_json_loading_failed
    raise UnsupportedMediaType(
werkzeug.exceptions.UnsupportedMediaType: 415 Unsupported Media Type: Did not attempt to load JSON data because the request Content-Type was not 'application/json'.

2025-07-08 21:39:03,505 - __main__ - INFO - 响应状态: 500
2025-07-08 21:39:03,506 - werkzeug - INFO - 127.0.0.1 - - [08/Jul/2025 21:39:03] "[35m[1mPOST /api/analysis/analyze HTTP/1.1[0m" 500 -
2025-07-08 22:09:13,349 - __main__ - INFO - 收到请求: GET http://localhost:5000/health
2025-07-08 22:09:13,350 - __main__ - INFO - 响应状态: 404
2025-07-08 22:09:13,351 - werkzeug - INFO - 127.0.0.1 - - [08/Jul/2025 22:09:13] "[33mGET /health HTTP/1.1[0m" 404 -
2025-07-08 22:09:27,578 - __main__ - INFO - 收到请求: GET http://localhost:5000/
2025-07-08 22:09:27,578 - __main__ - INFO - 响应状态: 404
2025-07-08 22:09:27,579 - werkzeug - INFO - 127.0.0.1 - - [08/Jul/2025 22:09:27] "[33mGET / HTTP/1.1[0m" 404 -
2025-07-08 22:09:42,239 - __main__ - INFO - 收到请求: POST http://localhost:5000/api/analysis/analyze
2025-07-08 22:09:42,239 - __main__ - INFO - 请求数据: {'ticker': 'NVDA', 'config': {}, 'date': '2025-01-08'}
2025-07-08 22:09:42,239 - __main__ - INFO - 执行股票分析: symbol=None, analysis_date=2025-01-08, custom_config={}
2025-07-08 22:09:42,239 - __main__ - INFO - 响应状态: 400
2025-07-08 22:09:42,240 - werkzeug - INFO - 127.0.0.1 - - [08/Jul/2025 22:09:42] "[31m[1mPOST /api/analysis/analyze HTTP/1.1[0m" 400 -
2025-07-08 22:10:31,344 - __main__ - INFO - 收到请求: POST http://localhost:5000/api/analysis/analyze
2025-07-08 22:10:31,344 - __main__ - INFO - 请求数据: {'symbol': 'NVDA', 'config': {}, 'date': '2025-01-08'}
2025-07-08 22:10:31,345 - __main__ - INFO - 执行股票分析: symbol=NVDA, analysis_date=2025-01-08, custom_config={}
2025-07-08 22:10:32,701 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-07-08 22:10:34,454 - __main__ - ERROR - 操作失败: 执行股票分析
2025-07-08 22:10:34,455 - __main__ - ERROR - 错误信息: name 'printf' is not defined
2025-07-08 22:10:34,455 - __main__ - ERROR - 错误详情: Traceback (most recent call last):
  File "D:\project\TradingAgents\api_server.py", line 553, in post
    _, decision = ta.propagate(symbol, analysis_date)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\TradingAgents\tradingagents\graph\trading_graph.py", line 174, in propagate
    for chunk in self.graph.stream(init_agent_state, **args):
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\langgraph\pregel\__init__.py", line 2542, in stream
    for _ in runner.tick(
             ^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\langgraph\pregel\runner.py", line 162, in tick
    run_with_retry(
  File "D:\python\Lib\site-packages\langgraph\pregel\retry.py", line 42, in run_with_retry
    return task.proc.invoke(task.input, config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\langgraph\utils\runnable.py", line 623, in invoke
    input = context.run(step.invoke, input, config, **kwargs)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\langgraph\utils\runnable.py", line 377, in invoke
    ret = self.func(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\TradingAgents\tradingagents\agents\analysts\market_analyst.py", line 77, in market_analyst_node
    result = chain.invoke(state["messages"])
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\langchain_core\runnables\base.py", line 3047, in invoke
    input_ = context.run(step.invoke, input_, config)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\langchain_core\runnables\base.py", line 5431, in invoke
    return self.bound.invoke(
           ^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\langchain_core\language_models\chat_models.py", line 378, in invoke
    self.generate_prompt(
  File "D:\python\Lib\site-packages\langchain_core\language_models\chat_models.py", line 963, in generate_prompt
    return self.generate(prompt_messages, stop=stop, callbacks=callbacks, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\langchain_core\language_models\chat_models.py", line 782, in generate
    self._generate_with_cache(
  File "D:\python\Lib\site-packages\langchain_core\language_models\chat_models.py", line 1028, in _generate_with_cache
    result = self._generate(
             ^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\langchain_openai\chat_models\base.py", line 1130, in _generate
    response = self.client.create(**payload)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\openai\_utils\_utils.py", line 287, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\openai\resources\chat\completions\completions.py", line 1087, in create
    return self._post(
           ^^^^^^^^^^^
  File "D:\python\Lib\site-packages\openai\_base_client.py", line 1249, in post
    printf(opts.json_data,)
    ^^^^^^
NameError: name 'printf' is not defined. Did you mean: 'print'?
During task with name 'Market Analyst' and id '27410800-0771-53fe-d153-e28778be40e3'

2025-07-08 22:10:34,456 - __main__ - INFO - 响应状态: 500
2025-07-08 22:10:34,456 - werkzeug - INFO - 127.0.0.1 - - [08/Jul/2025 22:10:34] "[35m[1mPOST /api/analysis/analyze HTTP/1.1[0m" 500 -
2025-07-08 22:18:14,336 - __main__ - INFO - TradingAgents API 服务器启动
2025-07-08 22:18:14,345 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-07-08 22:18:14,345 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-08 22:18:14,346 - werkzeug - INFO -  * Restarting with stat
2025-07-08 22:18:16,718 - __main__ - INFO - TradingAgents API 服务器启动
2025-07-08 22:18:16,722 - werkzeug - WARNING -  * Debugger is active!
2025-07-08 22:18:16,725 - werkzeug - INFO -  * Debugger PIN: 110-************-07-08 22:20:14,426 - __main__ - INFO - 收到请求: GET http://localhost:5000/api/analysis/analyze
2025-07-08 22:20:14,427 - __main__ - INFO - 响应状态: 405
2025-07-08 22:20:14,429 - werkzeug - INFO - 127.0.0.1 - - [08/Jul/2025 22:20:14] "[31m[1mGET /api/analysis/analyze HTTP/1.1[0m" 405 -
2025-07-08 22:20:14,528 - __main__ - INFO - 收到请求: GET http://localhost:5000/favicon.ico
2025-07-08 22:20:14,529 - __main__ - INFO - 响应状态: 404
2025-07-08 22:20:14,529 - werkzeug - INFO - 127.0.0.1 - - [08/Jul/2025 22:20:14] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-07-08 22:31:11,230 - __main__ - INFO - 收到请求: GET http://localhost:5000/health
2025-07-08 22:31:11,230 - __main__ - INFO - 响应状态: 404
2025-07-08 22:31:11,230 - werkzeug - INFO - 127.0.0.1 - - [08/Jul/2025 22:31:11] "[33mGET /health HTTP/1.1[0m" 404 -
2025-07-08 22:32:08,283 - werkzeug - INFO -  * Detected change in 'D:\\project\\TradingAgents\\api_server.py', reloading
2025-07-08 22:32:08,551 - werkzeug - INFO -  * Restarting with stat
2025-07-08 22:32:11,045 - __main__ - INFO - TradingAgents API 服务器启动
2025-07-08 22:32:11,050 - werkzeug - WARNING -  * Debugger is active!
2025-07-08 22:32:11,054 - werkzeug - INFO -  * Debugger PIN: 110-************-07-08 22:32:21,477 - __main__ - INFO - 收到请求: POST http://localhost:5000/api/analysis/analyze
2025-07-08 22:32:21,477 - __main__ - INFO - 请求数据: {'symbol': 'AAPL', 'config': {}, 'date': '2024-01-01'}
2025-07-08 22:32:21,477 - __main__ - INFO - 执行股票分析: symbol=AAPL, analysis_date=2024-01-01, custom_config={}
2025-07-08 22:32:22,793 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-07-08 22:32:24,545 - __main__ - ERROR - 操作失败: 执行股票分析
2025-07-08 22:32:24,545 - __main__ - ERROR - 错误信息: name 'printf' is not defined
2025-07-08 22:32:24,546 - __main__ - ERROR - 错误详情: Traceback (most recent call last):
  File "D:\project\TradingAgents\api_server.py", line 553, in post
    _, decision = ta.propagate(symbol, analysis_date)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\TradingAgents\tradingagents\graph\trading_graph.py", line 174, in propagate
    for chunk in self.graph.stream(init_agent_state, **args):
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\langgraph\pregel\__init__.py", line 2542, in stream
    for _ in runner.tick(
             ^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\langgraph\pregel\runner.py", line 162, in tick
    run_with_retry(
  File "D:\python\Lib\site-packages\langgraph\pregel\retry.py", line 42, in run_with_retry
    return task.proc.invoke(task.input, config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\langgraph\utils\runnable.py", line 623, in invoke
    input = context.run(step.invoke, input, config, **kwargs)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\langgraph\utils\runnable.py", line 377, in invoke
    ret = self.func(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\TradingAgents\tradingagents\agents\analysts\market_analyst.py", line 77, in market_analyst_node
    result = chain.invoke(state["messages"])
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\langchain_core\runnables\base.py", line 3047, in invoke
    input_ = context.run(step.invoke, input_, config)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\langchain_core\runnables\base.py", line 5431, in invoke
    return self.bound.invoke(
           ^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\langchain_core\language_models\chat_models.py", line 378, in invoke
    self.generate_prompt(
  File "D:\python\Lib\site-packages\langchain_core\language_models\chat_models.py", line 963, in generate_prompt
    return self.generate(prompt_messages, stop=stop, callbacks=callbacks, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\langchain_core\language_models\chat_models.py", line 782, in generate
    self._generate_with_cache(
  File "D:\python\Lib\site-packages\langchain_core\language_models\chat_models.py", line 1028, in _generate_with_cache
    result = self._generate(
             ^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\langchain_openai\chat_models\base.py", line 1130, in _generate
    response = self.client.create(**payload)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\openai\_utils\_utils.py", line 287, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\openai\resources\chat\completions\completions.py", line 1087, in create
    return self._post(
           ^^^^^^^^^^^
  File "D:\python\Lib\site-packages\openai\_base_client.py", line 1249, in post
    printf(opts.json_data,)
    ^^^^^^
NameError: name 'printf' is not defined. Did you mean: 'print'?
During task with name 'Market Analyst' and id '82e8c1da-0356-d2bf-31cd-432a319b4056'

2025-07-08 22:32:24,547 - __main__ - INFO - 响应状态: 500
2025-07-08 22:32:24,556 - werkzeug - INFO - 127.0.0.1 - - [08/Jul/2025 22:32:24] "[35m[1mPOST /api/analysis/analyze HTTP/1.1[0m" 500 -
2025-07-08 22:33:58,680 - __main__ - INFO - 收到请求: POST http://127.0.0.1:5000/api/analysis/analyze
2025-07-08 22:33:58,681 - __main__ - INFO - 请求数据: {'symbol': 'NVDA', 'date': '2025-06-30'}
2025-07-08 22:33:58,681 - __main__ - INFO - 执行股票分析: symbol=NVDA, analysis_date=2025-06-30, custom_config={}
2025-07-08 22:33:59,167 - __main__ - ERROR - 操作失败: 执行股票分析
2025-07-08 22:33:59,167 - __main__ - ERROR - 错误信息: Collection [bull_memory] already exists
2025-07-08 22:33:59,167 - __main__ - ERROR - 错误详情: Traceback (most recent call last):
  File "D:\project\TradingAgents\api_server.py", line 549, in post
    ta = TradingAgentsGraph(debug=True, config=config)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\TradingAgents\tradingagents\graph\trading_graph.py", line 79, in __init__
    self.bull_memory = FinancialSituationMemory("bull_memory", self.config)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\TradingAgents\tradingagents\agents\utils\memory.py", line 17, in __init__
    self.situation_collection = self.chroma_client.create_collection(name=name)
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\chromadb\api\client.py", line 168, in create_collection
    model = self._server.create_collection(
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\chromadb\api\rust.py", line 227, in create_collection
    collection = self.bindings.create_collection(
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
chromadb.errors.InternalError: Collection [bull_memory] already exists

2025-07-08 22:33:59,169 - __main__ - INFO - 响应状态: 500
2025-07-08 22:33:59,170 - werkzeug - INFO - 127.0.0.1 - - [08/Jul/2025 22:33:59] "[35m[1mPOST /api/analysis/analyze HTTP/1.1[0m" 500 -
2025-07-08 22:34:25,673 - __main__ - INFO - 收到请求: POST http://127.0.0.1:5000/api/analysis/analyze
2025-07-08 22:34:25,673 - __main__ - INFO - 请求数据: {'symbol': 'NVDA', 'date': '2025-06-30'}
2025-07-08 22:34:25,674 - __main__ - INFO - 执行股票分析: symbol=NVDA, analysis_date=2025-06-30, custom_config={}
2025-07-08 22:34:26,098 - __main__ - ERROR - 操作失败: 执行股票分析
2025-07-08 22:34:26,099 - __main__ - ERROR - 错误信息: Collection [bull_memory] already exists
2025-07-08 22:34:26,099 - __main__ - ERROR - 错误详情: Traceback (most recent call last):
  File "D:\project\TradingAgents\api_server.py", line 549, in post
    ta = TradingAgentsGraph(debug=True, config=config)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\TradingAgents\tradingagents\graph\trading_graph.py", line 79, in __init__
    self.bull_memory = FinancialSituationMemory("bull_memory", self.config)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\TradingAgents\tradingagents\agents\utils\memory.py", line 17, in __init__
    self.situation_collection = self.chroma_client.create_collection(name=name)
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\chromadb\api\client.py", line 168, in create_collection
    model = self._server.create_collection(
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\chromadb\api\rust.py", line 227, in create_collection
    collection = self.bindings.create_collection(
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
chromadb.errors.InternalError: Collection [bull_memory] already exists

2025-07-08 22:34:26,102 - __main__ - INFO - 响应状态: 500
2025-07-08 22:34:26,102 - werkzeug - INFO - 127.0.0.1 - - [08/Jul/2025 22:34:26] "[35m[1mPOST /api/analysis/analyze HTTP/1.1[0m" 500 -
2025-07-08 22:49:01,827 - werkzeug - INFO -  * Detected change in 'D:\\project\\TradingAgents\\tradingagents\\agents\\utils\\memory.py', reloading
2025-07-08 22:49:02,248 - werkzeug - INFO -  * Restarting with stat
2025-07-08 22:49:07,287 - __main__ - INFO - TradingAgents API 服务器启动
2025-07-08 22:49:07,292 - werkzeug - WARNING -  * Debugger is active!
2025-07-08 22:49:07,295 - werkzeug - INFO -  * Debugger PIN: 110-************-07-08 22:49:50,558 - werkzeug - INFO -  * Detected change in 'D:\\project\\TradingAgents\\tradingagents\\agents\\utils\\memory.py', reloading
2025-07-08 22:49:50,810 - werkzeug - INFO -  * Restarting with stat
2025-07-08 22:49:53,198 - __main__ - INFO - TradingAgents API 服务器启动
2025-07-08 22:49:53,202 - werkzeug - WARNING -  * Debugger is active!
2025-07-08 22:49:53,204 - werkzeug - INFO -  * Debugger PIN: 110-************-07-08 22:50:06,288 - werkzeug - INFO -  * Detected change in 'D:\\project\\TradingAgents\\api_server.py', reloading
2025-07-08 22:50:06,542 - werkzeug - INFO -  * Restarting with stat
2025-07-08 22:50:08,963 - __main__ - INFO - TradingAgents API 服务器启动
2025-07-08 22:50:08,967 - werkzeug - WARNING -  * Debugger is active!
2025-07-08 22:50:08,970 - werkzeug - INFO -  * Debugger PIN: 110-************-07-08 22:50:39,150 - werkzeug - INFO -  * Detected change in 'D:\\project\\TradingAgents\\tradingagents\\graph\\trading_graph.py', reloading
2025-07-08 22:50:39,418 - werkzeug - INFO -  * Restarting with stat
2025-07-08 22:50:41,863 - __main__ - INFO - TradingAgents API 服务器启动
2025-07-08 22:50:41,871 - werkzeug - WARNING -  * Debugger is active!
2025-07-08 22:50:41,874 - werkzeug - INFO -  * Debugger PIN: 110-************-07-08 22:50:58,982 - werkzeug - INFO -  * Detected change in 'D:\\project\\TradingAgents\\api_server.py', reloading
2025-07-08 22:50:59,254 - werkzeug - INFO -  * Restarting with stat
2025-07-08 22:51:01,759 - __main__ - INFO - TradingAgents API 服务器启动
2025-07-08 22:51:01,765 - werkzeug - WARNING -  * Debugger is active!
2025-07-08 22:51:01,767 - werkzeug - INFO -  * Debugger PIN: 110-************-07-08 22:51:54,048 - werkzeug - INFO -  * Detected change in 'D:\\project\\TradingAgents\\api_server.py', reloading
2025-07-08 22:51:54,321 - werkzeug - INFO -  * Restarting with stat
2025-07-08 22:51:56,753 - __main__ - INFO - TradingAgents API 服务器启动
2025-07-08 22:51:56,758 - werkzeug - WARNING -  * Debugger is active!
2025-07-08 22:51:56,761 - werkzeug - INFO -  * Debugger PIN: 110-************-07-08 22:52:47,980 - __main__ - INFO - 收到请求: POST http://127.0.0.1:5000/api/analysis/analyze
2025-07-08 22:52:47,980 - __main__ - INFO - 请求数据: {'symbol': 'NVDA', 'date': '2025-06-30'}
2025-07-08 22:52:47,980 - __main__ - INFO - 执行股票分析: symbol=NVDA, analysis_date=2025-06-30, custom_config={}
2025-07-08 22:52:49,374 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-07-08 22:52:51,534 - __main__ - ERROR - 操作失败: 执行股票分析
2025-07-08 22:52:51,534 - __main__ - ERROR - 错误信息: name 'printf' is not defined
2025-07-08 22:52:51,534 - __main__ - ERROR - 错误详情: Traceback (most recent call last):
  File "D:\project\TradingAgents\api_server.py", line 554, in post
    _, decision = ta.propagate(symbol, analysis_date)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\TradingAgents\tradingagents\graph\trading_graph.py", line 206, in propagate
    for chunk in self.graph.stream(init_agent_state, **args):
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\langgraph\pregel\__init__.py", line 2542, in stream
    for _ in runner.tick(
             ^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\langgraph\pregel\runner.py", line 162, in tick
    run_with_retry(
  File "D:\python\Lib\site-packages\langgraph\pregel\retry.py", line 42, in run_with_retry
    return task.proc.invoke(task.input, config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\langgraph\utils\runnable.py", line 623, in invoke
    input = context.run(step.invoke, input, config, **kwargs)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\langgraph\utils\runnable.py", line 377, in invoke
    ret = self.func(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\TradingAgents\tradingagents\agents\analysts\market_analyst.py", line 77, in market_analyst_node
    result = chain.invoke(state["messages"])
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\langchain_core\runnables\base.py", line 3047, in invoke
    input_ = context.run(step.invoke, input_, config)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\langchain_core\runnables\base.py", line 5431, in invoke
    return self.bound.invoke(
           ^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\langchain_core\language_models\chat_models.py", line 378, in invoke
    self.generate_prompt(
  File "D:\python\Lib\site-packages\langchain_core\language_models\chat_models.py", line 963, in generate_prompt
    return self.generate(prompt_messages, stop=stop, callbacks=callbacks, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\langchain_core\language_models\chat_models.py", line 782, in generate
    self._generate_with_cache(
  File "D:\python\Lib\site-packages\langchain_core\language_models\chat_models.py", line 1028, in _generate_with_cache
    result = self._generate(
             ^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\langchain_openai\chat_models\base.py", line 1130, in _generate
    response = self.client.create(**payload)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\openai\_utils\_utils.py", line 287, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\openai\resources\chat\completions\completions.py", line 1087, in create
    return self._post(
           ^^^^^^^^^^^
  File "D:\python\Lib\site-packages\openai\_base_client.py", line 1249, in post
    printf(opts.json_data,)
    ^^^^^^
NameError: name 'printf' is not defined. Did you mean: 'print'?
During task with name 'Market Analyst' and id '8dd48490-be20-9c6d-81c4-e2cbd1327eae'

2025-07-08 22:52:51,535 - __main__ - INFO - 响应状态: 500
2025-07-08 22:52:51,536 - werkzeug - INFO - 127.0.0.1 - - [08/Jul/2025 22:52:51] "[35m[1mPOST /api/analysis/analyze HTTP/1.1[0m" 500 -
2025-07-08 22:54:48,740 - werkzeug - INFO -  * Detected change in 'D:\\project\\TradingAgents\\api_server.py', reloading
2025-07-08 22:54:49,286 - werkzeug - INFO -  * Restarting with stat
2025-07-08 22:54:51,695 - __main__ - INFO - TradingAgents API 服务器启动
2025-07-08 22:54:51,700 - werkzeug - WARNING -  * Debugger is active!
2025-07-08 22:54:51,703 - werkzeug - INFO -  * Debugger PIN: 110-************-07-08 22:55:58,370 - __main__ - INFO - 收到请求: POST http://127.0.0.1:5000/api/analysis/analyze
2025-07-08 22:55:58,370 - __main__ - INFO - 请求数据: {'symbol': 'NVDA', 'date': '2025-06-30'}
2025-07-08 22:55:58,370 - __main__ - INFO - 执行股票分析: symbol=NVDA, analysis_date=2025-06-30, custom_config={}
2025-07-08 22:55:59,738 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-07-08 22:56:02,234 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions/chat/completions "HTTP/1.1 404 Not Found"
2025-07-08 22:56:02,241 - __main__ - ERROR - 操作失败: 执行股票分析
2025-07-08 22:56:02,241 - __main__ - ERROR - 错误信息: Error code: 404 - {'error': {'message': 'Invalid URL (POST /v1/chat/completions/chat/completions)', 'type': 'invalid_request_error', 'code': ''}}
2025-07-08 22:56:02,241 - __main__ - ERROR - 错误详情: Traceback (most recent call last):
  File "D:\project\TradingAgents\api_server.py", line 566, in post
    _, decision = ta.propagate(symbol, analysis_date)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\TradingAgents\tradingagents\graph\trading_graph.py", line 206, in propagate
    for chunk in self.graph.stream(init_agent_state, **args):
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\langgraph\pregel\__init__.py", line 2542, in stream
    for _ in runner.tick(
             ^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\langgraph\pregel\runner.py", line 162, in tick
    run_with_retry(
  File "D:\python\Lib\site-packages\langgraph\pregel\retry.py", line 42, in run_with_retry
    return task.proc.invoke(task.input, config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\langgraph\utils\runnable.py", line 623, in invoke
    input = context.run(step.invoke, input, config, **kwargs)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\langgraph\utils\runnable.py", line 377, in invoke
    ret = self.func(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\TradingAgents\tradingagents\agents\analysts\market_analyst.py", line 77, in market_analyst_node
    result = chain.invoke(state["messages"])
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\langchain_core\runnables\base.py", line 3047, in invoke
    input_ = context.run(step.invoke, input_, config)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\langchain_core\runnables\base.py", line 5431, in invoke
    return self.bound.invoke(
           ^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\langchain_core\language_models\chat_models.py", line 378, in invoke
    self.generate_prompt(
  File "D:\python\Lib\site-packages\langchain_core\language_models\chat_models.py", line 963, in generate_prompt
    return self.generate(prompt_messages, stop=stop, callbacks=callbacks, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\langchain_core\language_models\chat_models.py", line 782, in generate
    self._generate_with_cache(
  File "D:\python\Lib\site-packages\langchain_core\language_models\chat_models.py", line 1028, in _generate_with_cache
    result = self._generate(
             ^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\langchain_openai\chat_models\base.py", line 1130, in _generate
    response = self.client.create(**payload)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\openai\_utils\_utils.py", line 287, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\openai\resources\chat\completions\completions.py", line 1087, in create
    return self._post(
           ^^^^^^^^^^^
  File "D:\python\Lib\site-packages\openai\_base_client.py", line 1249, in post
    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\openai\_base_client.py", line 1037, in request
    raise self._make_status_error_from_response(err.response) from None
openai.NotFoundError: Error code: 404 - {'error': {'message': 'Invalid URL (POST /v1/chat/completions/chat/completions)', 'type': 'invalid_request_error', 'code': ''}}
During task with name 'Market Analyst' and id 'dea003ae-0d8a-d943-5379-1d1375f84715'

2025-07-08 22:56:02,244 - __main__ - INFO - 响应状态: 500
2025-07-08 22:56:02,244 - werkzeug - INFO - 127.0.0.1 - - [08/Jul/2025 22:56:02] "[35m[1mPOST /api/analysis/analyze HTTP/1.1[0m" 500 -
2025-07-08 22:57:08,485 - werkzeug - INFO -  * Detected change in 'D:\\project\\TradingAgents\\tradingagents\\agents\\utils\\agent_utils.py', reloading
2025-07-08 22:57:09,036 - werkzeug - INFO -  * Restarting with stat
2025-07-08 22:57:11,550 - __main__ - INFO - TradingAgents API 服务器启动
2025-07-08 22:57:11,554 - werkzeug - WARNING -  * Debugger is active!
2025-07-08 22:57:11,557 - werkzeug - INFO -  * Debugger PIN: 110-************-07-08 22:57:17,602 - werkzeug - INFO -  * Detected change in 'D:\\project\\TradingAgents\\tradingagents\\default_config.py', reloading
2025-07-08 22:57:17,870 - werkzeug - INFO -  * Restarting with stat
2025-07-08 22:57:20,268 - __main__ - INFO - TradingAgents API 服务器启动
2025-07-08 22:57:20,273 - werkzeug - WARNING -  * Debugger is active!
2025-07-08 22:57:20,276 - werkzeug - INFO -  * Debugger PIN: 110-************-07-08 22:57:27,323 - werkzeug - INFO -  * Detected change in 'D:\\project\\TradingAgents\\api_server.py', reloading
2025-07-08 22:57:27,608 - werkzeug - INFO -  * Restarting with stat
2025-07-08 22:57:30,152 - __main__ - INFO - TradingAgents API 服务器启动
2025-07-08 22:57:30,159 - werkzeug - WARNING -  * Debugger is active!
2025-07-08 22:57:30,162 - werkzeug - INFO -  * Debugger PIN: 110-************-07-08 22:57:31,821 - __main__ - INFO - 收到请求: POST http://127.0.0.1:5000/api/analysis/analyze
2025-07-08 22:57:31,821 - __main__ - INFO - 请求数据: {'symbol': 'NVDA', 'date': '2025-06-30'}
2025-07-08 22:57:31,821 - __main__ - INFO - 执行股票分析: symbol=NVDA, analysis_date=2025-06-30, custom_config={}
2025-07-08 22:57:33,189 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-07-08 22:57:52,410 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-08 22:58:18,207 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-08 22:58:21,033 - yfinance - ERROR - 
1 Failed download:
2025-07-08 22:58:21,034 - yfinance - ERROR - ['NVDA']: YFRateLimitError('Too Many Requests. Rate limited. Try after a while.')
2025-07-08 22:59:03,319 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-08 22:59:06,524 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-08 22:59:40,232 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:00:10,267 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:00:15,583 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:00:16,420 - openai._base_client - INFO - Retrying request to /chat/completions in 0.390079 seconds
2025-07-08 23:02:20,065 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:02:23,151 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:04:51,206 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:04:53,942 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:05:09,087 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:05:46,915 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:06:08,290 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:06:12,518 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/embeddings "HTTP/1.1 200 OK"
2025-07-08 23:06:55,390 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:06:59,661 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/embeddings "HTTP/1.1 200 OK"
2025-07-08 23:07:24,606 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:07:28,795 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/embeddings "HTTP/1.1 200 OK"
2025-07-08 23:07:50,442 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:07:50,447 - openai._base_client - INFO - Retrying request to /chat/completions in 0.462398 seconds
2025-07-08 23:08:13,017 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:09:03,145 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:09:05,098 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/embeddings "HTTP/1.1 200 OK"
2025-07-08 23:09:18,740 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:09:34,403 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:09:49,788 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:10:23,170 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:10:27,568 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/embeddings "HTTP/1.1 200 OK"
2025-07-08 23:10:40,999 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:10:45,606 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:10:45,606 - __main__ - INFO - 响应状态: 200
2025-07-08 23:10:45,607 - werkzeug - INFO - 127.0.0.1 - - [08/Jul/2025 23:10:45] "POST /api/analysis/analyze HTTP/1.1" 200 -
2025-07-08 23:30:34,788 - werkzeug - INFO -  * Detected change in 'D:\\project\\TradingAgents\\database_config.py', reloading
2025-07-08 23:30:35,386 - werkzeug - INFO -  * Restarting with stat
2025-07-08 23:30:37,723 - __main__ - INFO - TradingAgents API 服务器启动
2025-07-08 23:30:37,727 - werkzeug - WARNING -  * Debugger is active!
2025-07-08 23:30:37,730 - werkzeug - INFO -  * Debugger PIN: 110-************-07-08 23:30:53,827 - werkzeug - INFO -  * Detected change in 'D:\\project\\TradingAgents\\api_server.py', reloading
2025-07-08 23:30:54,137 - werkzeug - INFO -  * Restarting with stat
2025-07-08 23:30:56,511 - __main__ - INFO - TradingAgents API 服务器启动
2025-07-08 23:30:56,515 - werkzeug - WARNING -  * Debugger is active!
2025-07-08 23:30:56,519 - werkzeug - INFO -  * Debugger PIN: 110-************-07-08 23:31:11,615 - werkzeug - INFO -  * Detected change in 'D:\\project\\TradingAgents\\api_server.py', reloading
2025-07-08 23:31:11,869 - werkzeug - INFO -  * Restarting with stat
2025-07-08 23:31:14,292 - __main__ - INFO - TradingAgents API 服务器启动
2025-07-08 23:31:14,297 - werkzeug - WARNING -  * Debugger is active!
2025-07-08 23:31:14,300 - werkzeug - INFO -  * Debugger PIN: 110-************-07-08 23:31:30,415 - werkzeug - INFO -  * Detected change in 'D:\\project\\TradingAgents\\api_server.py', reloading
2025-07-08 23:31:30,681 - werkzeug - INFO -  * Restarting with stat
2025-07-08 23:31:33,000 - __main__ - INFO - TradingAgents API 服务器启动
2025-07-08 23:31:33,004 - werkzeug - WARNING -  * Debugger is active!
2025-07-08 23:31:33,007 - werkzeug - INFO -  * Debugger PIN: 110-************-07-08 23:32:11,271 - werkzeug - INFO -  * Detected change in 'D:\\project\\TradingAgents\\api_server.py', reloading
2025-07-08 23:32:11,527 - werkzeug - INFO -  * Restarting with stat
2025-07-08 23:32:13,913 - __main__ - INFO - TradingAgents API 服务器启动
2025-07-08 23:32:13,918 - werkzeug - WARNING -  * Debugger is active!
2025-07-08 23:32:13,921 - werkzeug - INFO -  * Debugger PIN: 110-************-07-08 23:35:41,386 - __main__ - INFO - 收到请求: POST http://localhost:5000/api/analysis/analyze
2025-07-08 23:35:41,388 - __main__ - ERROR - 操作失败: 执行股票分析
2025-07-08 23:35:41,388 - __main__ - ERROR - 错误信息: 415 Unsupported Media Type: Did not attempt to load JSON data because the request Content-Type was not 'application/json'.
2025-07-08 23:35:41,388 - __main__ - ERROR - 错误详情: Traceback (most recent call last):
  File "D:\project\TradingAgents\api_server.py", line 551, in post
    data = request.get_json()
           ^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\werkzeug\wrappers\request.py", line 604, in get_json
    return self.on_json_loading_failed(None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\flask\wrappers.py", line 214, in on_json_loading_failed
    return super().on_json_loading_failed(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\werkzeug\wrappers\request.py", line 647, in on_json_loading_failed
    raise UnsupportedMediaType(
werkzeug.exceptions.UnsupportedMediaType: 415 Unsupported Media Type: Did not attempt to load JSON data because the request Content-Type was not 'application/json'.

2025-07-08 23:35:41,389 - __main__ - INFO - 响应状态: 500
2025-07-08 23:35:41,389 - werkzeug - INFO - 127.0.0.1 - - [08/Jul/2025 23:35:41] "[35m[1mPOST /api/analysis/analyze HTTP/1.1[0m" 500 -
2025-07-08 23:35:54,496 - __main__ - INFO - 收到请求: POST http://127.0.0.1:5000/api/analysis/analyze
2025-07-08 23:35:54,496 - __main__ - INFO - 请求数据: {'symbol': 'NVDA', 'date': '2025-06-30'}
2025-07-08 23:35:54,496 - __main__ - INFO - 执行股票分析: symbol=NVDA, analysis_date=2025-06-30, custom_config={}
2025-07-08 23:35:55,902 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-07-08 23:36:30,036 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:37:06,093 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:37:08,434 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:37:59,047 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:39:26,627 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:39:32,955 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:39:48,317 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:40:09,494 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:40:13,367 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:41:14,005 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:41:14,007 - openai._base_client - INFO - Retrying request to /chat/completions in 0.498699 seconds
2025-07-08 23:42:29,656 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:42:32,677 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:42:53,911 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:43:22,294 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:43:22,374 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:43:31,772 - openai._base_client - INFO - Retrying request to /chat/completions in 0.498670 seconds
2025-07-08 23:43:40,906 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:44:22,241 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:44:36,928 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:44:39,816 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/embeddings "HTTP/1.1 200 OK"
2025-07-08 23:47:04,899 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:47:09,486 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/embeddings "HTTP/1.1 200 OK"
2025-07-08 23:47:36,028 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:47:38,291 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/embeddings "HTTP/1.1 200 OK"
2025-07-08 23:49:39,419 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:49:41,502 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/embeddings "HTTP/1.1 200 OK"
2025-07-08 23:49:48,549 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:50:08,017 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:50:22,178 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:50:32,299 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:50:36,684 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/embeddings "HTTP/1.1 200 OK"
2025-07-08 23:50:51,016 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:50:55,609 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-08 23:50:55,610 - __main__ - INFO - 响应状态: 200
2025-07-08 23:50:55,611 - werkzeug - INFO - 127.0.0.1 - - [08/Jul/2025 23:50:55] "POST /api/analysis/analyze HTTP/1.1" 200 -
2025-07-08 23:58:18,313 - werkzeug - INFO -  * Detected change in 'D:\\project\\TradingAgents\\api_server.py', reloading
2025-07-08 23:58:18,948 - werkzeug - INFO -  * Restarting with stat
2025-07-08 23:58:21,399 - __main__ - INFO - TradingAgents API 服务器启动
2025-07-08 23:58:21,403 - werkzeug - WARNING -  * Debugger is active!
2025-07-08 23:58:21,407 - werkzeug - INFO -  * Debugger PIN: 110-************-07-08 23:59:31,853 - werkzeug - INFO -  * Detected change in 'D:\\project\\TradingAgents\\api_server.py', reloading
2025-07-08 23:59:32,125 - werkzeug - INFO -  * Restarting with stat
2025-07-08 23:59:34,566 - __main__ - INFO - TradingAgents API 服务器启动
2025-07-08 23:59:34,570 - werkzeug - WARNING -  * Debugger is active!
2025-07-08 23:59:34,573 - werkzeug - INFO -  * Debugger PIN: 110-************-07-09 00:00:10,839 - werkzeug - INFO -  * Detected change in 'D:\\project\\TradingAgents\\api_server.py', reloading
2025-07-09 00:00:11,083 - werkzeug - INFO -  * Restarting with stat
2025-07-09 00:00:13,415 - __main__ - INFO - TradingAgents API 服务器启动
2025-07-09 00:00:13,419 - werkzeug - WARNING -  * Debugger is active!
2025-07-09 00:00:13,422 - werkzeug - INFO -  * Debugger PIN: 110-************-07-09 00:04:24,912 - werkzeug - INFO -  * Detected change in 'D:\\project\\TradingAgents\\api_server.py', reloading
2025-07-09 00:04:25,240 - werkzeug - INFO -  * Restarting with stat
2025-07-09 00:04:31,827 - __main__ - INFO - TradingAgents API 服务器启动
2025-07-09 00:04:31,831 - werkzeug - WARNING -  * Debugger is active!
2025-07-09 00:04:31,834 - werkzeug - INFO -  * Debugger PIN: 110-************-07-09 00:05:14,956 - __main__ - INFO - 收到请求: GET http://localhost:5000/health
2025-07-09 00:05:14,957 - __main__ - INFO - 健康检查请求
2025-07-09 00:05:14,957 - __main__ - INFO - 响应状态: 200
2025-07-09 00:05:14,958 - werkzeug - INFO - 127.0.0.1 - - [09/Jul/2025 00:05:14] "GET /health HTTP/1.1" 200 -
2025-07-09 00:05:16,997 - __main__ - INFO - 收到请求: POST http://localhost:5000/api/analysis/analyze
2025-07-09 00:05:16,998 - __main__ - INFO - 请求数据: {'symbol': 'SQLITE_API_TEST', 'date': '2025-06-30'}
2025-07-09 00:05:16,998 - __main__ - INFO - 执行股票分析: symbol=SQLITE_API_TEST, analysis_date=2025-06-30, custom_config={}
2025-07-09 00:05:18,333 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-07-09 00:05:20,754 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions/chat/completions "HTTP/1.1 404 Not Found"
2025-07-09 00:05:20,758 - __main__ - ERROR - 操作失败: 执行股票分析
2025-07-09 00:05:20,758 - __main__ - ERROR - 错误信息: Error code: 404 - {'error': {'message': 'Invalid URL (POST /v1/chat/completions/chat/completions)', 'type': 'invalid_request_error', 'code': ''}}
2025-07-09 00:05:20,759 - __main__ - ERROR - 错误详情: Traceback (most recent call last):
  File "D:\project\TradingAgents\api_server.py", line 584, in post
    _, decision = ta.propagate(symbol, analysis_date)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\TradingAgents\tradingagents\graph\trading_graph.py", line 206, in propagate
    for chunk in self.graph.stream(init_agent_state, **args):
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\langgraph\pregel\__init__.py", line 2542, in stream
    for _ in runner.tick(
             ^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\langgraph\pregel\runner.py", line 162, in tick
    run_with_retry(
  File "D:\python\Lib\site-packages\langgraph\pregel\retry.py", line 42, in run_with_retry
    return task.proc.invoke(task.input, config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\langgraph\utils\runnable.py", line 623, in invoke
    input = context.run(step.invoke, input, config, **kwargs)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\langgraph\utils\runnable.py", line 377, in invoke
    ret = self.func(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\TradingAgents\tradingagents\agents\analysts\market_analyst.py", line 77, in market_analyst_node
    result = chain.invoke(state["messages"])
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\langchain_core\runnables\base.py", line 3047, in invoke
    input_ = context.run(step.invoke, input_, config)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\langchain_core\runnables\base.py", line 5431, in invoke
    return self.bound.invoke(
           ^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\langchain_core\language_models\chat_models.py", line 378, in invoke
    self.generate_prompt(
  File "D:\python\Lib\site-packages\langchain_core\language_models\chat_models.py", line 963, in generate_prompt
    return self.generate(prompt_messages, stop=stop, callbacks=callbacks, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\langchain_core\language_models\chat_models.py", line 782, in generate
    self._generate_with_cache(
  File "D:\python\Lib\site-packages\langchain_core\language_models\chat_models.py", line 1028, in _generate_with_cache
    result = self._generate(
             ^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\langchain_openai\chat_models\base.py", line 1130, in _generate
    response = self.client.create(**payload)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\openai\_utils\_utils.py", line 287, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\openai\resources\chat\completions\completions.py", line 1087, in create
    return self._post(
           ^^^^^^^^^^^
  File "D:\python\Lib\site-packages\openai\_base_client.py", line 1249, in post
    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\openai\_base_client.py", line 1037, in request
    raise self._make_status_error_from_response(err.response) from None
openai.NotFoundError: Error code: 404 - {'error': {'message': 'Invalid URL (POST /v1/chat/completions/chat/completions)', 'type': 'invalid_request_error', 'code': ''}}
During task with name 'Market Analyst' and id 'a6a75f02-f109-9971-2e54-7f3081b42ab4'

2025-07-09 00:05:20,760 - __main__ - INFO - 响应状态: 500
2025-07-09 00:05:20,760 - werkzeug - INFO - 127.0.0.1 - - [09/Jul/2025 00:05:20] "[35m[1mPOST /api/analysis/analyze HTTP/1.1[0m" 500 -
2025-07-09 00:05:35,791 - __main__ - INFO - TradingAgents API 服务器启动
2025-07-09 00:05:35,798 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-07-09 00:05:35,798 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-09 00:05:35,798 - werkzeug - INFO -  * Restarting with stat
2025-07-09 00:05:42,244 - __main__ - INFO - TradingAgents API 服务器启动
2025-07-09 00:05:42,249 - werkzeug - WARNING -  * Debugger is active!
2025-07-09 00:05:42,252 - werkzeug - INFO -  * Debugger PIN: 110-************-07-09 00:05:44,244 - werkzeug - INFO -  * Detected change in 'D:\\project\\TradingAgents\\database_config.py', reloading
2025-07-09 00:05:44,273 - werkzeug - INFO -  * Detected change in 'D:\\project\\TradingAgents\\database_config.py', reloading
2025-07-09 00:05:44,567 - werkzeug - INFO -  * Restarting with stat
2025-07-09 00:05:44,601 - werkzeug - INFO -  * Restarting with stat
2025-07-09 00:05:51,098 - __main__ - INFO - TradingAgents API 服务器启动
2025-07-09 00:05:51,103 - werkzeug - WARNING -  * Debugger is active!
2025-07-09 00:05:51,106 - werkzeug - INFO -  * Debugger PIN: 110-************-07-09 00:05:51,113 - __main__ - INFO - TradingAgents API 服务器启动
2025-07-09 00:05:51,117 - werkzeug - WARNING -  * Debugger is active!
2025-07-09 00:05:51,120 - werkzeug - INFO -  * Debugger PIN: 110-************-07-09 00:06:26,611 - __main__ - INFO - 收到请求: GET http://localhost:5000/health
2025-07-09 00:06:26,612 - __main__ - INFO - 健康检查请求
2025-07-09 00:06:26,612 - __main__ - INFO - 响应状态: 200
2025-07-09 00:06:26,613 - werkzeug - INFO - 127.0.0.1 - - [09/Jul/2025 00:06:26] "GET /health HTTP/1.1" 200 -
2025-07-09 00:06:30,571 - werkzeug - INFO -  * Detected change in 'D:\\project\\TradingAgents\\database_config.py', reloading
2025-07-09 00:06:30,573 - werkzeug - INFO -  * Detected change in 'D:\\project\\TradingAgents\\database_config.py', reloading
2025-07-09 00:06:30,860 - werkzeug - INFO -  * Restarting with stat
2025-07-09 00:06:30,860 - werkzeug - INFO -  * Restarting with stat
2025-07-09 00:06:37,265 - __main__ - INFO - TradingAgents API 服务器启动
2025-07-09 00:06:37,269 - werkzeug - WARNING -  * Debugger is active!
2025-07-09 00:06:37,271 - werkzeug - INFO -  * Debugger PIN: 110-************-07-09 00:06:37,377 - __main__ - INFO - TradingAgents API 服务器启动
2025-07-09 00:06:37,381 - werkzeug - WARNING -  * Debugger is active!
2025-07-09 00:06:37,384 - werkzeug - INFO -  * Debugger PIN: 110-************-07-09 00:07:07,694 - __main__ - INFO - 收到请求: GET http://localhost:5000/health
2025-07-09 00:07:07,694 - __main__ - INFO - 健康检查请求
2025-07-09 00:07:07,695 - __main__ - INFO - 响应状态: 200
2025-07-09 00:07:07,695 - werkzeug - INFO - 127.0.0.1 - - [09/Jul/2025 00:07:07] "GET /health HTTP/1.1" 200 -
2025-07-09 00:07:09,750 - __main__ - INFO - 收到请求: GET http://localhost:5000/api/analysis/quick/TEST_DB?date=2025-06-30
2025-07-09 00:07:09,750 - __main__ - INFO - 快速分析: symbol=TEST_DB, analysis_date=2025-06-30
2025-07-09 00:07:11,025 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-07-09 00:07:13,640 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions/chat/completions "HTTP/1.1 404 Not Found"
2025-07-09 00:07:13,645 - __main__ - ERROR - 操作失败: 快速分析
2025-07-09 00:07:13,645 - __main__ - ERROR - 错误信息: Error code: 404 - {'error': {'message': 'Invalid URL (POST /v1/chat/completions/chat/completions)', 'type': 'invalid_request_error', 'code': ''}}
2025-07-09 00:07:13,645 - __main__ - ERROR - 错误详情: Traceback (most recent call last):
  File "D:\project\TradingAgents\api_server.py", line 755, in get
    _, decision = ta.propagate(symbol, analysis_date)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\TradingAgents\tradingagents\graph\trading_graph.py", line 216, in propagate
    final_state = self.graph.invoke(init_agent_state, **args)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\langgraph\pregel\__init__.py", line 2852, in invoke
    for chunk in self.stream(
                 ^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\langgraph\pregel\__init__.py", line 2542, in stream
    for _ in runner.tick(
             ^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\langgraph\pregel\runner.py", line 162, in tick
    run_with_retry(
  File "D:\python\Lib\site-packages\langgraph\pregel\retry.py", line 42, in run_with_retry
    return task.proc.invoke(task.input, config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\langgraph\utils\runnable.py", line 623, in invoke
    input = context.run(step.invoke, input, config, **kwargs)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\langgraph\utils\runnable.py", line 377, in invoke
    ret = self.func(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\TradingAgents\tradingagents\agents\analysts\market_analyst.py", line 77, in market_analyst_node
    result = chain.invoke(state["messages"])
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\langchain_core\runnables\base.py", line 3047, in invoke
    input_ = context.run(step.invoke, input_, config)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\langchain_core\runnables\base.py", line 5431, in invoke
    return self.bound.invoke(
           ^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\langchain_core\language_models\chat_models.py", line 378, in invoke
    self.generate_prompt(
  File "D:\python\Lib\site-packages\langchain_core\language_models\chat_models.py", line 963, in generate_prompt
    return self.generate(prompt_messages, stop=stop, callbacks=callbacks, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\langchain_core\language_models\chat_models.py", line 782, in generate
    self._generate_with_cache(
  File "D:\python\Lib\site-packages\langchain_core\language_models\chat_models.py", line 1028, in _generate_with_cache
    result = self._generate(
             ^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\langchain_openai\chat_models\base.py", line 1130, in _generate
    response = self.client.create(**payload)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\openai\_utils\_utils.py", line 287, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\openai\resources\chat\completions\completions.py", line 1087, in create
    return self._post(
           ^^^^^^^^^^^
  File "D:\python\Lib\site-packages\openai\_base_client.py", line 1249, in post
    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\openai\_base_client.py", line 1037, in request
    raise self._make_status_error_from_response(err.response) from None
openai.NotFoundError: Error code: 404 - {'error': {'message': 'Invalid URL (POST /v1/chat/completions/chat/completions)', 'type': 'invalid_request_error', 'code': ''}}
During task with name 'Market Analyst' and id 'bba1bcad-5f84-982a-bc24-8950ea29c885'

2025-07-09 00:07:13,646 - __main__ - INFO - 响应状态: 500
2025-07-09 00:07:13,646 - werkzeug - INFO - 127.0.0.1 - - [09/Jul/2025 00:07:13] "[35m[1mGET /api/analysis/quick/TEST_DB?date=2025-06-30 HTTP/1.1[0m" 500 -
2025-07-09 00:07:17,487 - werkzeug - INFO -  * Detected change in 'D:\\project\\TradingAgents\\database_config.py', reloading
2025-07-09 00:07:17,605 - werkzeug - INFO -  * Detected change in 'D:\\project\\TradingAgents\\database_config.py', reloading
2025-07-09 00:07:17,749 - werkzeug - INFO -  * Restarting with stat
2025-07-09 00:07:18,236 - werkzeug - INFO -  * Restarting with stat
2025-07-09 00:07:20,231 - __main__ - INFO - TradingAgents API 服务器启动
2025-07-09 00:07:20,235 - werkzeug - WARNING -  * Debugger is active!
2025-07-09 00:07:20,238 - werkzeug - INFO -  * Debugger PIN: 110-************-07-09 00:07:20,589 - __main__ - INFO - TradingAgents API 服务器启动
2025-07-09 00:07:20,593 - werkzeug - WARNING -  * Debugger is active!
2025-07-09 00:07:20,596 - werkzeug - INFO -  * Debugger PIN: 110-************-07-09 00:07:23,457 - __main__ - INFO - 收到请求: GET http://localhost:5000/health
2025-07-09 00:07:23,457 - __main__ - INFO - 健康检查请求
2025-07-09 00:07:23,457 - __main__ - INFO - 响应状态: 200
2025-07-09 00:07:23,458 - werkzeug - INFO - 127.0.0.1 - - [09/Jul/2025 00:07:23] "GET /health HTTP/1.1" 200 -
2025-07-09 00:07:25,500 - __main__ - INFO - 收到请求: GET http://localhost:5000/api/analysis/quick/TEST_DB?date=2025-06-30
2025-07-09 00:07:25,500 - __main__ - INFO - 快速分析: symbol=TEST_DB, analysis_date=2025-06-30
2025-07-09 00:07:26,785 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-07-09 00:07:29,222 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions/chat/completions "HTTP/1.1 404 Not Found"
2025-07-09 00:07:29,232 - __main__ - ERROR - 操作失败: 快速分析
2025-07-09 00:07:29,233 - __main__ - ERROR - 错误信息: Error code: 404 - {'error': {'message': 'Invalid URL (POST /v1/chat/completions/chat/completions)', 'type': 'invalid_request_error', 'code': ''}}
2025-07-09 00:07:29,233 - __main__ - ERROR - 错误详情: Traceback (most recent call last):
  File "D:\project\TradingAgents\api_server.py", line 755, in get
    _, decision = ta.propagate(symbol, analysis_date)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\TradingAgents\tradingagents\graph\trading_graph.py", line 216, in propagate
    final_state = self.graph.invoke(init_agent_state, **args)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\langgraph\pregel\__init__.py", line 2852, in invoke
    for chunk in self.stream(
                 ^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\langgraph\pregel\__init__.py", line 2542, in stream
    for _ in runner.tick(
             ^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\langgraph\pregel\runner.py", line 162, in tick
    run_with_retry(
  File "D:\python\Lib\site-packages\langgraph\pregel\retry.py", line 42, in run_with_retry
    return task.proc.invoke(task.input, config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\langgraph\utils\runnable.py", line 623, in invoke
    input = context.run(step.invoke, input, config, **kwargs)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\langgraph\utils\runnable.py", line 377, in invoke
    ret = self.func(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\project\TradingAgents\tradingagents\agents\analysts\market_analyst.py", line 77, in market_analyst_node
    result = chain.invoke(state["messages"])
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\langchain_core\runnables\base.py", line 3047, in invoke
    input_ = context.run(step.invoke, input_, config)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\langchain_core\runnables\base.py", line 5431, in invoke
    return self.bound.invoke(
           ^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\langchain_core\language_models\chat_models.py", line 378, in invoke
    self.generate_prompt(
  File "D:\python\Lib\site-packages\langchain_core\language_models\chat_models.py", line 963, in generate_prompt
    return self.generate(prompt_messages, stop=stop, callbacks=callbacks, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\langchain_core\language_models\chat_models.py", line 782, in generate
    self._generate_with_cache(
  File "D:\python\Lib\site-packages\langchain_core\language_models\chat_models.py", line 1028, in _generate_with_cache
    result = self._generate(
             ^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\langchain_openai\chat_models\base.py", line 1130, in _generate
    response = self.client.create(**payload)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\openai\_utils\_utils.py", line 287, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\openai\resources\chat\completions\completions.py", line 1087, in create
    return self._post(
           ^^^^^^^^^^^
  File "D:\python\Lib\site-packages\openai\_base_client.py", line 1249, in post
    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\python\Lib\site-packages\openai\_base_client.py", line 1037, in request
    raise self._make_status_error_from_response(err.response) from None
openai.NotFoundError: Error code: 404 - {'error': {'message': 'Invalid URL (POST /v1/chat/completions/chat/completions)', 'type': 'invalid_request_error', 'code': ''}}
During task with name 'Market Analyst' and id '3eaf18d7-9a82-b564-2338-a0661dc11786'

2025-07-09 00:07:29,234 - __main__ - INFO - 响应状态: 500
2025-07-09 00:07:29,235 - werkzeug - INFO - 127.0.0.1 - - [09/Jul/2025 00:07:29] "[35m[1mGET /api/analysis/quick/TEST_DB?date=2025-06-30 HTTP/1.1[0m" 500 -
2025-07-09 00:07:33,318 - werkzeug - INFO -  * Detected change in 'D:\\project\\TradingAgents\\api_server.py', reloading
2025-07-09 00:07:33,567 - werkzeug - INFO -  * Restarting with stat
2025-07-09 00:07:33,676 - werkzeug - INFO -  * Detected change in 'D:\\project\\TradingAgents\\api_server.py', reloading
2025-07-09 00:07:34,437 - werkzeug - INFO -  * Restarting with stat
2025-07-09 00:07:35,976 - __main__ - INFO - TradingAgents API 服务器启动
2025-07-09 00:07:35,982 - werkzeug - WARNING -  * Debugger is active!
2025-07-09 00:07:35,985 - werkzeug - INFO -  * Debugger PIN: 110-************-07-09 00:07:36,788 - __main__ - INFO - TradingAgents API 服务器启动
2025-07-09 00:07:36,792 - werkzeug - WARNING -  * Debugger is active!
2025-07-09 00:07:36,795 - werkzeug - INFO -  * Debugger PIN: 110-************-07-09 00:08:54,344 - werkzeug - INFO -  * Detected change in 'D:\\project\\TradingAgents\\api_server.py', reloading
2025-07-09 00:08:54,464 - werkzeug - INFO -  * Detected change in 'D:\\project\\TradingAgents\\api_server.py', reloading
2025-07-09 00:08:54,634 - werkzeug - INFO -  * Restarting with stat
2025-07-09 00:08:54,750 - werkzeug - INFO -  * Restarting with stat
2025-07-09 00:13:54,617 - __main__ - INFO - TradingAgents API 服务器启动
2025-07-09 00:13:54,627 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-07-09 00:13:54,627 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-09 00:13:54,628 - werkzeug - INFO -  * Restarting with stat
2025-07-09 00:13:57,022 - __main__ - INFO - TradingAgents API 服务器启动
2025-07-09 00:13:57,026 - werkzeug - WARNING -  * Debugger is active!
2025-07-09 00:13:57,029 - werkzeug - INFO -  * Debugger PIN: 110-************-07-09 00:14:19,741 - __main__ - INFO - 收到请求: GET http://localhost:5000/health
2025-07-09 00:14:19,741 - __main__ - INFO - 健康检查请求
2025-07-09 00:14:19,742 - __main__ - INFO - 响应状态: 200
2025-07-09 00:14:19,743 - werkzeug - INFO - 127.0.0.1 - - [09/Jul/2025 00:14:19] "GET /health HTTP/1.1" 200 -
2025-07-09 00:14:21,810 - __main__ - INFO - 收到请求: POST http://localhost:5000/api/analysis/analyze
2025-07-09 00:14:21,810 - __main__ - INFO - 请求数据: {'symbol': 'AAPL', 'date': '2025-06-30'}
2025-07-09 00:14:21,811 - __main__ - INFO - 执行股票分析: symbol=AAPL, analysis_date=2025-06-30, custom_config={}
2025-07-09 00:14:23,108 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-07-09 00:14:53,201 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:15:25,281 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:15:35,017 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:16:16,769 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:16:19,393 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:16:43,954 - __main__ - INFO - 收到请求: POST http://127.0.0.1:5000/api/analysis/analyze
2025-07-09 00:16:43,954 - __main__ - INFO - 请求数据: {'symbol': 'NVDA', 'date': '2025-06-30'}
2025-07-09 00:16:43,954 - __main__ - INFO - 执行股票分析: symbol=NVDA, analysis_date=2025-06-30, custom_config={}
2025-07-09 00:16:50,813 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:16:58,519 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:17:25,781 - __main__ - INFO - TradingAgents API 服务器启动
2025-07-09 00:17:25,788 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-07-09 00:17:25,788 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-09 00:17:25,789 - werkzeug - INFO -  * Restarting with stat
2025-07-09 00:17:28,209 - __main__ - INFO - TradingAgents API 服务器启动
2025-07-09 00:17:28,214 - werkzeug - WARNING -  * Debugger is active!
2025-07-09 00:17:28,217 - werkzeug - INFO -  * Debugger PIN: 110-************-07-09 00:17:40,932 - __main__ - INFO - 收到请求: POST http://127.0.0.1:5000/api/analysis/analyze
2025-07-09 00:17:40,933 - __main__ - INFO - 请求数据: {'symbol': 'NVDA', 'date': '2025-06-30'}
2025-07-09 00:17:40,933 - __main__ - INFO - 执行股票分析: symbol=NVDA, analysis_date=2025-06-30, custom_config={}
2025-07-09 00:17:42,266 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-07-09 00:17:58,929 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:18:08,831 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:18:42,550 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:18:44,536 - yfinance - ERROR - 
1 Failed download:
2025-07-09 00:18:44,536 - yfinance - ERROR - ['NVDA']: YFRateLimitError('Too Many Requests. Rate limited. Try after a while.')
2025-07-09 00:19:13,418 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:19:19,210 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:19:47,060 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:20:11,218 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:20:25,350 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:20:33,237 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:20:34,098 - openai._base_client - INFO - Retrying request to /chat/completions in 0.492159 seconds
2025-07-09 00:20:44,770 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:21:05,499 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:21:09,112 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:23:37,126 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:23:41,616 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:23:42,542 - openai._base_client - INFO - Retrying request to /chat/completions in 0.445868 seconds
2025-07-09 00:24:07,725 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:24:42,343 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:24:58,415 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:25:01,937 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/embeddings "HTTP/1.1 200 OK"
2025-07-09 00:25:18,122 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:25:22,891 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/embeddings "HTTP/1.1 200 OK"
2025-07-09 00:25:44,781 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:25:49,511 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/embeddings "HTTP/1.1 200 OK"
2025-07-09 00:26:10,347 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:26:15,358 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/embeddings "HTTP/1.1 200 OK"
2025-07-09 00:26:23,872 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:26:44,978 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:26:59,808 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:27:15,921 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:27:21,343 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/embeddings "HTTP/1.1 200 OK"
2025-07-09 00:27:39,389 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:27:44,453 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:27:44,461 - database_config - INFO - 保存分析消息成功: 8daf8a3a-2632-4623-9911-d5ea5f064ce1 for NVDA
2025-07-09 00:27:44,462 - __main__ - INFO - 分析结果已保存到数据库: 8daf8a3a-2632-4623-9911-d5ea5f064ce1
2025-07-09 00:27:44,462 - __main__ - INFO - 响应状态: 200
2025-07-09 00:27:44,462 - werkzeug - INFO - 127.0.0.1 - - [09/Jul/2025 00:27:44] "POST /api/analysis/analyze HTTP/1.1" 200 -
2025-07-09 00:31:14,855 - werkzeug - INFO -  * Detected change in 'D:\\project\\TradingAgents\\api_server.py', reloading
2025-07-09 00:31:15,656 - werkzeug - INFO -  * Restarting with stat
2025-07-09 00:31:18,110 - __main__ - INFO - TradingAgents API 服务器启动
2025-07-09 00:31:18,114 - werkzeug - WARNING -  * Debugger is active!
2025-07-09 00:31:18,118 - werkzeug - INFO -  * Debugger PIN: 110-************-07-09 00:31:39,272 - werkzeug - INFO -  * Detected change in 'D:\\project\\TradingAgents\\api_server.py', reloading
2025-07-09 00:31:39,546 - werkzeug - INFO -  * Restarting with stat
2025-07-09 00:31:41,880 - __main__ - INFO - TradingAgents API 服务器启动
2025-07-09 00:31:41,886 - werkzeug - WARNING -  * Debugger is active!
2025-07-09 00:31:41,889 - werkzeug - INFO -  * Debugger PIN: 110-************-07-09 00:31:54,975 - werkzeug - INFO -  * Detected change in 'D:\\project\\TradingAgents\\api_server.py', reloading
2025-07-09 00:31:55,223 - werkzeug - INFO -  * Restarting with stat
2025-07-09 00:31:57,669 - __main__ - INFO - TradingAgents API 服务器启动
2025-07-09 00:31:57,674 - werkzeug - WARNING -  * Debugger is active!
2025-07-09 00:31:57,677 - werkzeug - INFO -  * Debugger PIN: 110-************-07-09 00:34:50,712 - __main__ - INFO - TradingAgents API 服务器启动
2025-07-09 00:34:50,721 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-07-09 00:34:50,721 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-09 00:34:50,722 - werkzeug - INFO -  * Restarting with stat
2025-07-09 00:34:53,120 - __main__ - INFO - TradingAgents API 服务器启动
2025-07-09 00:34:53,124 - werkzeug - WARNING -  * Debugger is active!
2025-07-09 00:34:53,127 - werkzeug - INFO -  * Debugger PIN: 110-************-07-09 00:35:22,635 - __main__ - INFO - 收到请求: GET http://localhost:5000/health
2025-07-09 00:35:22,635 - __main__ - INFO - 健康检查请求
2025-07-09 00:35:22,635 - __main__ - INFO - 响应状态: 200
2025-07-09 00:35:22,637 - werkzeug - INFO - 127.0.0.1 - - [09/Jul/2025 00:35:22] "GET /health HTTP/1.1" 200 -
2025-07-09 00:35:24,674 - __main__ - INFO - 收到请求: POST http://localhost:5000/api/analysis/analyze
2025-07-09 00:35:24,674 - __main__ - INFO - 请求数据: {'symbol': 'AI_TEST', 'date': '2025-06-30'}
2025-07-09 00:35:24,675 - __main__ - INFO - 执行股票分析: symbol=AI_TEST, analysis_date=2025-06-30, custom_config={}
2025-07-09 00:35:25,965 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-07-09 00:35:44,673 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:35:56,023 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:36:34,789 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:36:37,949 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:37:06,995 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:37:52,148 - werkzeug - INFO -  * Detected change in 'D:\\project\\TradingAgents\\test_ai_messages.py', reloading
2025-07-09 00:37:52,430 - werkzeug - INFO -  * Restarting with stat
2025-07-09 00:37:54,927 - __main__ - INFO - TradingAgents API 服务器启动
2025-07-09 00:37:54,933 - werkzeug - WARNING -  * Debugger is active!
2025-07-09 00:37:54,936 - werkzeug - INFO -  * Debugger PIN: 110-************-07-09 00:37:56,951 - __main__ - INFO - 收到请求: GET http://localhost:5000/api/analysis/quick/QUICK_TEST?date=2025-06-30
2025-07-09 00:37:56,952 - __main__ - INFO - 快速分析: symbol=QUICK_TEST, analysis_date=2025-06-30
2025-07-09 00:37:58,261 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-07-09 00:38:02,222 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:38:09,019 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:38:12,821 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:38:17,347 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:38:19,830 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:38:22,757 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:38:34,634 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:38:47,955 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:38:59,154 - __main__ - INFO - TradingAgents API 服务器启动
2025-07-09 00:38:59,161 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-07-09 00:38:59,161 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-09 00:38:59,162 - werkzeug - INFO -  * Restarting with stat
2025-07-09 00:39:01,668 - __main__ - INFO - TradingAgents API 服务器启动
2025-07-09 00:39:01,674 - werkzeug - WARNING -  * Debugger is active!
2025-07-09 00:39:01,677 - werkzeug - INFO -  * Debugger PIN: 110-************-07-09 00:39:03,585 - __main__ - INFO - 收到请求: POST http://127.0.0.1:5000/api/analysis/analyze
2025-07-09 00:39:03,585 - __main__ - INFO - 请求数据: {'symbol': 'NVDA', 'date': '2025-06-30'}
2025-07-09 00:39:03,585 - __main__ - INFO - 执行股票分析: symbol=NVDA, analysis_date=2025-06-30, custom_config={}
2025-07-09 00:39:04,996 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-07-09 00:39:21,982 - __main__ - INFO - TradingAgents API 服务器启动
2025-07-09 00:39:21,993 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-07-09 00:39:21,994 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-09 00:39:21,994 - werkzeug - INFO -  * Restarting with stat
2025-07-09 00:39:24,364 - __main__ - INFO - TradingAgents API 服务器启动
2025-07-09 00:39:24,368 - werkzeug - WARNING -  * Debugger is active!
2025-07-09 00:39:24,372 - werkzeug - INFO -  * Debugger PIN: 110-************-07-09 00:39:30,425 - __main__ - INFO - 收到请求: POST http://127.0.0.1:5000/api/analysis/analyze
2025-07-09 00:39:30,425 - __main__ - INFO - 请求数据: {'symbol': 'NVDA', 'date': '2025-06-30'}
2025-07-09 00:39:30,426 - __main__ - INFO - 执行股票分析: symbol=NVDA, analysis_date=2025-06-30, custom_config={}
2025-07-09 00:39:31,830 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-07-09 00:39:55,482 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:39:57,892 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:40:26,311 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:41:09,099 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:41:26,309 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:41:33,618 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:41:34,476 - openai._base_client - INFO - Retrying request to /chat/completions in 0.463535 seconds
2025-07-09 00:41:51,131 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:42:14,360 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:42:17,308 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:42:43,667 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:42:47,780 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:42:48,608 - openai._base_client - INFO - Retrying request to /chat/completions in 0.382889 seconds
2025-07-09 00:43:04,985 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:43:56,196 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:44:09,053 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:44:13,178 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/embeddings "HTTP/1.1 200 OK"
2025-07-09 00:44:40,012 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:44:44,903 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/embeddings "HTTP/1.1 200 OK"
2025-07-09 00:45:00,774 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:45:04,943 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/embeddings "HTTP/1.1 200 OK"
2025-07-09 00:45:23,414 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:45:28,533 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/embeddings "HTTP/1.1 200 OK"
2025-07-09 00:45:35,344 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:45:48,413 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:46:04,293 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:46:18,748 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:46:23,872 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/embeddings "HTTP/1.1 200 OK"
2025-07-09 00:46:37,605 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:46:40,544 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:46:40,560 - database_config - INFO - 保存分析消息成功: 1f28b42b-bec8-44d4-adf7-6e0ed7f88e89 for NVDA
2025-07-09 00:46:40,565 - database_config - INFO - 保存分析消息成功: 944d5958-c940-4ae2-b6da-d60243393440 for NVDA
2025-07-09 00:46:40,566 - __main__ - INFO - 响应状态: 200
2025-07-09 00:46:40,566 - werkzeug - INFO - 127.0.0.1 - - [09/Jul/2025 00:46:40] "POST /api/analysis/analyze HTTP/1.1" 200 -
2025-07-09 00:49:29,730 - werkzeug - INFO -  * Detected change in 'D:\\project\\TradingAgents\\tradingagents\\graph\\trading_graph.py', reloading
2025-07-09 00:49:30,156 - werkzeug - INFO -  * Restarting with stat
2025-07-09 00:49:32,532 - __main__ - INFO - TradingAgents API 服务器启动
2025-07-09 00:49:32,537 - werkzeug - WARNING -  * Debugger is active!
2025-07-09 00:49:32,540 - werkzeug - INFO -  * Debugger PIN: 110-************-07-09 00:49:55,671 - werkzeug - INFO -  * Detected change in 'D:\\project\\TradingAgents\\api_server.py', reloading
2025-07-09 00:49:55,934 - werkzeug - INFO -  * Restarting with stat
2025-07-09 00:49:58,385 - __main__ - INFO - TradingAgents API 服务器启动
2025-07-09 00:49:58,390 - werkzeug - WARNING -  * Debugger is active!
2025-07-09 00:49:58,393 - werkzeug - INFO -  * Debugger PIN: 110-************-07-09 00:50:24,952 - __main__ - INFO - TradingAgents API 服务器启动
2025-07-09 00:50:24,961 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-07-09 00:50:24,961 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-09 00:50:24,962 - werkzeug - INFO -  * Restarting with stat
2025-07-09 00:50:27,421 - __main__ - INFO - TradingAgents API 服务器启动
2025-07-09 00:50:27,426 - werkzeug - WARNING -  * Debugger is active!
2025-07-09 00:50:27,429 - werkzeug - INFO -  * Debugger PIN: 110-************-07-09 00:54:59,666 - __main__ - INFO - 收到请求: GET http://localhost:5000/health
2025-07-09 00:54:59,666 - __main__ - INFO - 健康检查请求
2025-07-09 00:54:59,667 - __main__ - INFO - 响应状态: 200
2025-07-09 00:54:59,667 - werkzeug - INFO - 127.0.0.1 - - [09/Jul/2025 00:54:59] "GET /health HTTP/1.1" 200 -
2025-07-09 00:55:01,722 - __main__ - INFO - 收到请求: GET http://localhost:5000/api/analysis/quick/TEST_AI?date=2025-06-30
2025-07-09 00:55:01,722 - __main__ - INFO - 快速分析: symbol=TEST_AI, analysis_date=2025-06-30
2025-07-09 00:55:02,999 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-07-09 00:55:06,995 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:55:18,441 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:55:20,526 - yfinance - ERROR - 
1 Failed download:
2025-07-09 00:55:20,526 - yfinance - ERROR - 
1 Failed download:
2025-07-09 00:55:20,526 - yfinance - ERROR - 
1 Failed download:
2025-07-09 00:55:20,526 - yfinance - ERROR - ['TEST_AI']: YFRateLimitError('Too Many Requests. Rate limited. Try after a while.')
2025-07-09 00:55:20,526 - yfinance - ERROR - ['TEST_AI']: YFRateLimitError('Too Many Requests. Rate limited. Try after a while.')
2025-07-09 00:55:20,526 - yfinance - ERROR - ['TEST_AI']: YFRateLimitError('Too Many Requests. Rate limited. Try after a while.')
2025-07-09 00:55:20,531 - yfinance - ERROR - 
1 Failed download:
2025-07-09 00:55:20,531 - yfinance - ERROR - 
1 Failed download:
2025-07-09 00:55:20,531 - yfinance - ERROR - ['TEST_AI']: YFRateLimitError('Too Many Requests. Rate limited. Try after a while.')
2025-07-09 00:55:20,531 - yfinance - ERROR - ['TEST_AI']: YFRateLimitError('Too Many Requests. Rate limited. Try after a while.')
2025-07-09 00:55:20,532 - yfinance - ERROR - 
1 Failed download:
2025-07-09 00:55:20,532 - yfinance - ERROR - 
1 Failed download:
2025-07-09 00:55:20,533 - yfinance - ERROR - ['TEST_AI']: YFRateLimitError('Too Many Requests. Rate limited. Try after a while.')
2025-07-09 00:55:20,533 - yfinance - ERROR - ['TEST_AI']: YFRateLimitError('Too Many Requests. Rate limited. Try after a while.')
2025-07-09 00:55:30,438 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:55:32,181 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:55:34,992 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:55:45,226 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:55:47,148 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:55:47,973 - openai._base_client - INFO - Retrying request to /chat/completions in 0.490708 seconds
2025-07-09 00:55:50,622 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:56:04,253 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:56:06,330 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:56:07,142 - openai._base_client - INFO - Retrying request to /chat/completions in 0.383190 seconds
2025-07-09 00:56:09,486 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:56:18,053 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:56:24,722 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:56:26,839 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:56:27,656 - openai._base_client - INFO - Retrying request to /chat/completions in 0.492797 seconds
2025-07-09 00:56:30,089 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:56:39,595 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:56:41,783 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:56:53,263 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:56:54,853 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:56:55,666 - openai._base_client - INFO - Retrying request to /chat/completions in 0.496376 seconds
2025-07-09 00:56:58,775 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:57:33,727 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:57:44,368 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:57:54,800 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:57:57,534 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:58:02,280 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/embeddings "HTTP/1.1 200 OK"
2025-07-09 00:58:11,034 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:58:15,861 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/embeddings "HTTP/1.1 200 OK"
2025-07-09 00:58:24,177 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:58:29,330 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/embeddings "HTTP/1.1 200 OK"
2025-07-09 00:58:51,218 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:58:54,178 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/embeddings "HTTP/1.1 200 OK"
2025-07-09 00:58:57,082 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:59:04,592 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:59:20,977 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:59:27,300 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:59:31,938 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/embeddings "HTTP/1.1 200 OK"
2025-07-09 00:59:47,435 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:59:48,753 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 00:59:48,754 - __main__ - INFO - 响应状态: 200
2025-07-09 00:59:48,755 - werkzeug - INFO - 127.0.0.1 - - [09/Jul/2025 00:59:48] "GET /api/analysis/quick/TEST_AI?date=2025-06-30 HTTP/1.1" 200 -
2025-07-09 19:43:04,281 - __main__ - INFO - TradingAgents API 服务器启动
2025-07-09 19:43:04,290 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-07-09 19:43:04,290 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-09 19:43:04,291 - werkzeug - INFO -  * Restarting with stat
2025-07-09 19:43:06,712 - __main__ - INFO - TradingAgents API 服务器启动
2025-07-09 19:43:06,716 - werkzeug - WARNING -  * Debugger is active!
2025-07-09 19:43:06,719 - werkzeug - INFO -  * Debugger PIN: 110-************-07-09 19:43:54,233 - __main__ - INFO - 收到请求: POST http://127.0.0.1:5000/api/analysis/analyze
2025-07-09 19:43:54,233 - __main__ - INFO - 请求数据: {'symbol': 'NVDA', 'date': '2025-06-30'}
2025-07-09 19:43:54,233 - __main__ - INFO - 执行股票分析: symbol=NVDA, analysis_date=2025-06-30, custom_config={}
2025-07-09 19:43:55,755 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-07-09 19:44:13,278 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 19:44:43,865 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 19:44:47,604 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 19:45:18,590 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 19:45:27,803 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 19:45:36,381 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 19:45:37,387 - openai._base_client - INFO - Retrying request to /chat/completions in 0.409973 seconds
2025-07-09 19:45:53,077 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 19:46:13,599 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 19:46:18,471 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 19:48:33,675 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 19:48:36,960 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 19:48:37,815 - openai._base_client - INFO - Retrying request to /chat/completions in 0.418313 seconds
2025-07-09 19:48:54,488 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 19:49:20,482 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 19:49:32,480 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 19:49:35,346 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/embeddings "HTTP/1.1 200 OK"
2025-07-09 19:49:51,929 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 19:49:56,512 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/embeddings "HTTP/1.1 200 OK"
2025-07-09 19:50:16,801 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 19:50:18,841 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/embeddings "HTTP/1.1 200 OK"
2025-07-09 19:50:35,669 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 19:50:37,942 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/embeddings "HTTP/1.1 200 OK"
2025-07-09 19:50:43,363 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 19:50:59,671 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 19:51:17,949 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 19:51:28,436 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 19:51:30,709 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/embeddings "HTTP/1.1 200 OK"
2025-07-09 19:51:45,457 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 19:51:48,180 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 19:51:48,201 - database_config - INFO - 保存分析消息成功: 959836c2-addc-422f-af7c-e6d73a22403e for NVDA
2025-07-09 19:51:48,211 - database_config - INFO - 保存分析消息成功: 8bf907ed-fa95-4448-869a-d28a6a0b0abc for NVDA
2025-07-09 19:51:48,217 - database_config - INFO - 保存分析消息成功: 8c4b3226-f92e-4b2b-bede-2222da0ad234 for NVDA
2025-07-09 19:51:48,223 - database_config - INFO - 保存分析消息成功: 7ba06fbe-081c-4f8a-8cd5-a19341e2380d for NVDA
2025-07-09 19:51:48,229 - database_config - INFO - 保存分析消息成功: 99218894-ef8c-4ecb-8625-8e9c158ae3a4 for NVDA
2025-07-09 19:51:48,235 - database_config - INFO - 保存分析消息成功: fed621ad-a9a8-4c0b-a9c9-84c933851742 for NVDA
2025-07-09 19:51:48,243 - database_config - INFO - 保存分析消息成功: d6bea7e1-35b8-4184-9e38-6dcabaefe2f5 for NVDA
2025-07-09 19:51:48,249 - database_config - INFO - 保存分析消息成功: 0486f3de-7a91-4695-8687-22fcb321c9cb for NVDA
2025-07-09 19:51:48,256 - database_config - INFO - 保存分析消息成功: a9eb6df7-f96e-4242-a57e-baed6d3de37b for NVDA
2025-07-09 19:51:48,307 - database_config - INFO - 保存分析消息成功: 8d40a80a-eddf-49ed-af95-cdce0c406a9d for NVDA
2025-07-09 19:51:48,322 - database_config - INFO - 保存分析消息成功: d415715d-e986-41c5-b241-88283e9d9de2 for NVDA
2025-07-09 19:51:48,329 - database_config - INFO - 保存分析消息成功: c586a9e7-6927-4f2a-8ec2-72835d497d72 for NVDA
2025-07-09 19:51:48,334 - database_config - INFO - 保存分析消息成功: de04e7f7-ba4d-4749-b056-c9e069aed2d7 for NVDA
2025-07-09 19:51:48,342 - database_config - INFO - 保存分析消息成功: 82dd992b-3240-4ebb-9f5c-52ca5f44560b for NVDA
2025-07-09 19:51:48,349 - database_config - INFO - 保存分析消息成功: 22cd6f8c-7a02-492a-808f-621b95bce02c for NVDA
2025-07-09 19:51:48,355 - database_config - INFO - 保存分析消息成功: d8c2e0db-dd65-46c7-9a4e-d829dcd86a9b for NVDA
2025-07-09 19:51:48,365 - database_config - INFO - 保存分析消息成功: d98f7be7-dbf9-4c71-98c4-d658a1c295fb for NVDA
2025-07-09 19:51:48,371 - database_config - INFO - 保存分析消息成功: 9c51a9af-83b6-4759-99fb-8287655bef5f for NVDA
2025-07-09 19:51:48,377 - database_config - INFO - 保存分析消息成功: 8adc6990-68ab-4e7d-92ee-4b46a974ab43 for NVDA
2025-07-09 19:51:48,385 - database_config - INFO - 保存分析消息成功: d5eaaf91-26b3-4a53-85d7-be40c003426c for NVDA
2025-07-09 19:51:48,395 - database_config - INFO - 保存分析消息成功: 168fb001-6631-4a80-a828-a28111e6f2da for NVDA
2025-07-09 19:51:48,403 - database_config - INFO - 保存分析消息成功: 8b220535-9d63-426e-b6ee-1e55e826e022 for NVDA
2025-07-09 19:51:48,410 - database_config - INFO - 保存分析消息成功: 089790ed-719e-4273-9263-eac7463d3686 for NVDA
2025-07-09 19:51:48,417 - database_config - INFO - 保存分析消息成功: 934e92f0-4472-4677-985e-de21d822064b for NVDA
2025-07-09 19:51:48,425 - database_config - INFO - 保存分析消息成功: d2f02fd5-8a9d-46d2-8c7a-82587f83611b for NVDA
2025-07-09 19:51:48,432 - database_config - INFO - 保存分析消息成功: 84b0babd-4456-4319-bff6-1d3b4487b31f for NVDA
2025-07-09 19:51:48,441 - database_config - INFO - 保存分析消息成功: a7487504-54e6-4b65-ae6f-1c18748909e6 for NVDA
2025-07-09 19:51:48,449 - database_config - INFO - 保存分析消息成功: 01e54054-38fc-421c-b82b-fc3295bd8854 for NVDA
2025-07-09 19:51:48,456 - database_config - INFO - 保存分析消息成功: eb2fc00c-4d2d-4604-b828-bb784ed3c703 for NVDA
2025-07-09 19:51:48,460 - database_config - INFO - 保存分析消息成功: d2798871-49a7-4dcb-8fb0-fef806697490 for NVDA
2025-07-09 19:51:48,467 - database_config - INFO - 保存分析消息成功: 55b83b82-605e-414d-8ed2-e1db18a88547 for NVDA
2025-07-09 19:51:48,473 - database_config - INFO - 保存分析消息成功: b866147f-910d-41b9-99d7-701facebb7a4 for NVDA
2025-07-09 19:51:48,480 - database_config - INFO - 保存分析消息成功: edc6a190-f01a-466c-b3c7-35b59cb6d35b for NVDA
2025-07-09 19:51:48,487 - database_config - INFO - 保存分析消息成功: 34d93c03-094f-4cd2-bbc7-6c33de96f9af for NVDA
2025-07-09 19:51:48,559 - database_config - INFO - 保存分析消息成功: 382ecde7-c256-4d8e-9bc6-3a7a611a74dc for NVDA
2025-07-09 19:51:48,566 - database_config - INFO - 保存分析消息成功: 9230d965-20f5-488d-bbb4-3853d526bb64 for NVDA
2025-07-09 19:51:48,572 - database_config - INFO - 保存分析消息成功: cc2fe52f-780c-410a-bec4-50a83a966d94 for NVDA
2025-07-09 19:51:48,579 - database_config - INFO - 保存分析消息成功: 28aa4207-30d5-4745-bf5b-5eb30b2afe1c for NVDA
2025-07-09 19:51:48,586 - database_config - INFO - 保存分析消息成功: 53b4c3cd-488d-4bc3-8c2d-b9763981307c for NVDA
2025-07-09 19:51:48,658 - database_config - INFO - 保存分析消息成功: 50402daf-4797-4196-8f49-1d9daa406190 for NVDA
2025-07-09 19:51:48,716 - database_config - INFO - 保存分析消息成功: 28d8bdb8-2465-46cb-86b2-569b941e427f for NVDA
2025-07-09 19:51:48,723 - database_config - INFO - 保存分析消息成功: e3c50539-1b3a-4480-a605-7de1b1b92f71 for NVDA
2025-07-09 19:51:48,731 - database_config - INFO - 保存分析消息成功: 32031925-f936-4c57-875b-3676c5daac41 for NVDA
2025-07-09 19:51:48,737 - database_config - INFO - 保存分析消息成功: 11a63fab-4475-435e-9f17-830e00982d6f for NVDA
2025-07-09 19:51:48,744 - database_config - INFO - 保存分析消息成功: 920d2bf4-5d99-4955-9314-e5b205883bef for NVDA
2025-07-09 19:51:48,754 - database_config - INFO - 保存分析消息成功: 865cbd40-1b51-4ddb-87a3-33222709b45f for NVDA
2025-07-09 19:51:48,762 - database_config - INFO - 保存分析消息成功: 0d913085-b3ed-4ef6-bdba-5e5c5b59ccaa for NVDA
2025-07-09 19:51:48,768 - database_config - INFO - 保存分析消息成功: c31a48f7-04dc-4b71-8323-7c871fbdaf79 for NVDA
2025-07-09 19:51:48,775 - database_config - INFO - 保存分析消息成功: 8ee9532d-cd0d-4584-983b-0fd6cc06346c for NVDA
2025-07-09 19:51:48,784 - database_config - INFO - 保存分析消息成功: 9fa0c069-6f70-4557-b5ca-bdbbaebc3faf for NVDA
2025-07-09 19:51:48,795 - database_config - INFO - 保存分析消息成功: ea8e50d8-cd2a-4cde-a2dc-dbfdee105fd2 for NVDA
2025-07-09 19:51:48,802 - database_config - INFO - 保存分析消息成功: 95f4f9c3-a4a4-4cb5-a59e-2987fe8f4938 for NVDA
2025-07-09 19:51:48,811 - database_config - INFO - 保存分析消息成功: 1dbfef8e-82ca-40e7-86ed-1ad13772ad5c for NVDA
2025-07-09 19:51:48,817 - database_config - INFO - 保存分析消息成功: 18b92f59-5dcc-474a-a818-bdc409b84c7c for NVDA
2025-07-09 19:51:48,823 - database_config - INFO - 保存分析消息成功: 6626da8b-0dc8-48c4-a065-19010e33b980 for NVDA
2025-07-09 19:51:48,830 - database_config - INFO - 保存分析消息成功: 8ba6e34d-4735-41db-941e-59076e89de75 for NVDA
2025-07-09 19:51:48,837 - database_config - INFO - 保存分析消息成功: d5eb9a24-3d25-4794-a8b4-31274a450ebe for NVDA
2025-07-09 19:51:48,843 - database_config - INFO - 保存分析消息成功: b925f5c9-6556-4668-82ca-08ccacffe1a6 for NVDA
2025-07-09 19:51:48,854 - database_config - INFO - 保存分析消息成功: 8514aa0f-a641-4dbb-ad39-712ea688176f for NVDA
2025-07-09 19:51:48,861 - database_config - INFO - 保存分析消息成功: 221ddf32-47b2-40e8-9195-dc5e8e983757 for NVDA
2025-07-09 19:51:48,868 - database_config - INFO - 保存分析消息成功: fd1ffd85-0721-4c2d-9b5d-84c95fc12a11 for NVDA
2025-07-09 19:51:48,875 - database_config - INFO - 保存分析消息成功: 91a010d1-3822-465a-9157-fdb1771973b6 for NVDA
2025-07-09 19:51:48,892 - database_config - INFO - 保存分析消息成功: 741414fa-a2a4-46c0-b46f-01b27fc827ea for NVDA
2025-07-09 19:51:48,907 - database_config - INFO - 保存分析消息成功: 0c01cf64-963a-47af-ade4-382425245cf8 for NVDA
2025-07-09 19:51:48,917 - database_config - INFO - 保存分析消息成功: e35abd31-3785-4879-a566-9b5dc13cacc2 for NVDA
2025-07-09 19:51:48,918 - __main__ - INFO - 响应状态: 200
2025-07-09 19:51:48,919 - werkzeug - INFO - 127.0.0.1 - - [09/Jul/2025 19:51:48] "POST /api/analysis/analyze HTTP/1.1" 200 -
2025-07-09 22:16:15,840 - werkzeug - INFO -  * Detected change in 'D:\\project\\TradingAgents\\tradingagents\\agents\\researchers\\bull_researcher.py', reloading
2025-07-09 22:16:17,014 - werkzeug - INFO -  * Restarting with stat
2025-07-09 22:16:20,167 - __main__ - INFO - TradingAgents API 服务器启动
2025-07-09 22:16:20,176 - werkzeug - WARNING -  * Debugger is active!
2025-07-09 22:16:20,179 - werkzeug - INFO -  * Debugger PIN: 110-************-07-09 23:45:32,783 - __main__ - INFO - 收到请求: POST http://127.0.0.1:5000/api/analysis/analyze
2025-07-09 23:45:32,785 - __main__ - INFO - 请求数据: {'symbol': 'NVDA', 'date': '2025-07-03'}
2025-07-09 23:45:32,786 - __main__ - INFO - 执行股票分析: symbol=NVDA, analysis_date=2025-07-03, custom_config={}
2025-07-09 23:45:36,047 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-07-09 23:53:40,611 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-09 23:59:23,355 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-10 00:11:07,691 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-10 00:11:37,565 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-10 00:16:31,742 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-10 00:16:35,540 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-10 00:17:49,460 - httpx - INFO - HTTP Request: POST https://api.nuwaapi.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-10 00:19:39,861 - werkzeug - INFO -  * Detected change in 'D:\\project\\TradingAgents\\tradingagents\\dataflows\\akshare_utils.py', reloading
2025-07-10 00:19:50,110 - werkzeug - INFO -  * Restarting with stat
2025-07-10 00:19:55,207 - __main__ - INFO - TradingAgents API 服务器启动
2025-07-10 00:19:55,213 - werkzeug - WARNING -  * Debugger is active!
2025-07-10 00:19:55,216 - werkzeug - INFO -  * Debugger PIN: 110-************-07-10 00:20:28,437 - werkzeug - INFO -  * Detected change in 'D:\\project\\TradingAgents\\tradingagents\\dataflows\\akshare_utils.py', reloading
2025-07-10 00:20:28,679 - werkzeug - INFO -  * Restarting with stat
2025-07-10 00:20:31,021 - __main__ - INFO - TradingAgents API 服务器启动
2025-07-10 00:20:31,026 - werkzeug - WARNING -  * Debugger is active!
2025-07-10 00:20:31,029 - werkzeug - INFO -  * Debugger PIN: 110-************-07-10 00:21:02,305 - werkzeug - INFO -  * Detected change in 'D:\\project\\TradingAgents\\tradingagents\\dataflows\\interface.py', reloading
2025-07-10 00:21:02,579 - werkzeug - INFO -  * Restarting with stat
2025-07-10 00:21:48,928 - werkzeug - INFO -  * Restarting with stat
2025-07-10 00:22:37,164 - werkzeug - INFO -  * Restarting with stat
2025-07-10 00:23:30,649 - werkzeug - INFO -  * Restarting with stat
2025-07-10 00:24:23,949 - werkzeug - INFO -  * Restarting with stat
2025-07-10 00:24:52,370 - werkzeug - INFO -  * Restarting with stat
2025-07-10 00:25:19,967 - werkzeug - INFO -  * Restarting with stat
2025-07-10 00:27:48,408 - werkzeug - INFO -  * Restarting with stat
2025-07-10 00:29:13,773 - werkzeug - INFO -  * Restarting with stat
2025-07-10 00:31:54,381 - werkzeug - INFO -  * Restarting with stat
